# src/robot/motion.py
"""
运动控制专用模块

该模块专门负责机器人的运动控制，包括线性运动、关节运动和速度控制。

作者: Augment Agent
日期: 2025-07-15
"""

import sys
import os
# 添加lib目录到Python路径
lib_path = os.path.join(os.path.dirname(__file__), '..', '..', 'lib')
if lib_path not in sys.path:
    sys.path.append(lib_path)

try:
    import nrc_interface as nrc
except ImportError:
    nrc = None

from ..core.validators import ParameterValidator


class MotionController:
    """
    运动控制器
    
    专门负责机器人的运动控制功能。
    """
    
    def __init__(self, connection_manager, servo_controller, error_handler):
        """
        初始化运动控制器
        
        Args:
            connection_manager: 连接管理器实例
            servo_controller: 伺服控制器实例
            error_handler: 错误处理器实例
        """
        self.conn = connection_manager
        self.servo = servo_controller
        self.error_handler = error_handler
    
    def move_linear(self, target_pos, velocity, blending_radius):
        """
        发送一个阻塞式的线性运动指令 (MoveL)

        直接调用官方SDK的robot_movel函数，确保API一致性。
        程序会在此等待直到运动完成。

        Args:
            target_pos (list): 包含6个浮点数（X,Y,Z,A,B,C）的Python列表
            velocity (float): 运动速度 (单位: mm/s)
            blending_radius (float): 路径平滑过渡的半径/等级 (pl 参数)

        Returns:
            bool: 运动成功返回True，失败返回False
        """
        # 参数验证
        try:
            ParameterValidator.validate_position(target_pos, "目标位置")
            ParameterValidator.validate_velocity(velocity)
            ParameterValidator.validate_blending_radius(blending_radius)
        except ValueError as e:
            print(f"[错误] 参数错误: {e}")
            return False

        if self.conn.is_development_mode():
            print(f"[开发模式] 模拟线性移动至 {target_pos}")
            return True

        if not self.conn.is_robot_connected():
            print("[错误] 机器人未连接，无法执行运动")
            return False

        # 安全检查：验证伺服状态
        if not self.servo.check_servo_ready_for_motion():
            print("[错误] 伺服状态不适合执行运动，请先检查并使能伺服")
            return False

        if nrc is None:
            print("[警告] nrc_interface 不可用")
            return False

        print(f"正在执行线性移动至: {target_pos}")

        try:
            # 创建MoveCmd对象 - 按照官方SDK规范
            move_cmd = nrc.MoveCmd()

            # 创建VectorDouble对象并填充目标位置
            target_vector = nrc.VectorDouble()
            for pos in target_pos:
                target_vector.push_back(float(pos))

            # 填充MoveCmd对象的属性 - 按照官方文档
            move_cmd.targetPosValue = target_vector
            move_cmd.velocity = float(velocity)
            move_cmd.pl = float(blending_radius)
            move_cmd.coord = 1  # 直角坐标系
            move_cmd.acc = 100  # 默认加速度
            move_cmd.dec = 100  # 默认减速度
            move_cmd.toolNum = 1  # 默认工具
            move_cmd.userNum = 1  # 默认用户坐标系

            # 直接调用官方SDK的robot_movel函数
            result = nrc.robot_movel(self.conn.get_socket_fd(), move_cmd)

            if result == 0:  # SUCCESS
                print("[成功] 线性运动执行成功")
                return True
            else:
                print(f"[错误] 线性运动执行失败，错误代码: {result}")
                return False

        except Exception as e:
            print(f"[错误] 线性运动执行异常: {e}")
            return False
    
    def move_joint(self, target_pos, velocity, blending_radius):
        """
        发送一个阻塞式的关节运动指令 (MoveJ)
        
        增强的关节运动方法，包含伺服状态检查和安全验证。
        关节运动在3D打印中用于快速定位、避障和换层移动。
        程序会在此等待直到运动完成。
        
        Args:
            target_pos (list): 包含6个浮点数（X,Y,Z,A,B,C）的Python列表（笛卡尔坐标）
            velocity (float): 运动速度 (单位: mm/s)
            blending_radius (float): 路径平滑过渡的半径/等级 (pl 参数)
        
        Returns:
            bool: 运动成功返回True，失败返回False
        """
        # 参数验证
        try:
            ParameterValidator.validate_position(target_pos, "目标位置")
            ParameterValidator.validate_velocity(velocity)
            ParameterValidator.validate_blending_radius(blending_radius)
        except ValueError as e:
            print(f"[错误] 参数错误: {e}")
            return False
        
        if self.conn.is_development_mode():
            print(f"[提示] 开发模式：模拟关节移动至 {target_pos}")
            return True
        
        if not self.conn.is_robot_connected():
            print("[错误] 机器人未连接，无法执行关节运动")
            return False
        
        # 安全检查：验证伺服状态
        if not self.servo.check_servo_ready_for_motion():
            print("[错误] 伺服状态不适合执行运动，请先检查并使能伺服")
            return False
        
        if nrc is None:
            print("[警告] nrc_interface 不可用")
            return False
        
        print(f"正在执行关节移动至: {target_pos}")
        
        try:
            # 创建MoveCmd对象
            move_cmd = nrc.MoveCmd()
            
            # 创建VectorDouble对象并填充目标位置
            target_vector = nrc.VectorDouble()
            for pos in target_pos:
                target_vector.push_back(float(pos))
            
            # 填充MoveCmd对象的属性
            move_cmd.targetPosValue = target_vector
            move_cmd.velocity = float(velocity)
            move_cmd.pl = float(blending_radius)
            move_cmd.toolNum = 1  # 默认工具
            move_cmd.userNum = 1  # 默认坐标系
            
            # 执行关节运动
            result = nrc.robot_movej(self.conn.get_socket_fd(), move_cmd)
            
            if result == 0:  # SUCCESS
                print("[成功] 关节运动执行成功")
                return True
            else:
                print(f"[错误] 关节运动执行失败，错误代码: {result}")
                return False
                
        except Exception as e:
            print(f"[错误] 关节运动执行异常: {e}")
            return False
    
    def set_speed(self, speed_percentage):
        """
        设置机器人全局速度比例
        
        用于3D打印过程中动态调整运动速度。
        
        Args:
            speed_percentage (int): 速度比例，范围1-100 (%)
        
        Returns:
            bool: 设置成功返回True，失败返回False
        """
        # 参数验证
        try:
            ParameterValidator.validate_speed_percentage(speed_percentage)
        except ValueError as e:
            print(f"[错误] 参数错误: {e}")
            return False
        
        if self.conn.is_development_mode():
            print(f"[提示] 开发模式：模拟设置速度比例为 {speed_percentage}%")
            return True
        
        if not self.conn.is_robot_connected():
            print("[错误] 机器人未连接，无法设置速度")
            return False
        
        if nrc is None:
            print("[警告] nrc_interface 不可用")
            return False
        
        # 根据官方文档，速度参数应为整数，范围1-100
        speed_int = int(round(speed_percentage))
        
        try:
            print(f"设置机器人速度比例为: {speed_int}%")
            result = nrc.set_speed(self.conn.get_socket_fd(), speed_int)
            
            if result == 0:  # SUCCESS
                print(f"[成功] 速度比例设置成功: {speed_int}%")
                return True
            else:
                print(f"[错误] 设置速度比例失败，错误代码: {result}")
                return False
                
        except Exception as e:
            print(f"[错误] 设置速度比例异常: {e}")
            return False
    
    def get_speed(self):
        """
        获取机器人当前速度比例

        正确处理C++接口的输出参数，按照官方文档规范。

        Returns:
            int: 当前速度比例(%)，失败返回None
        """
        if self.conn.is_development_mode():
            print("[提示] 开发模式：模拟当前速度比例 50%")
            return 50

        if not self.conn.is_robot_connected():
            print("[错误] 机器人未连接，无法获取速度")
            return None

        if nrc is None:
            print("[警告] nrc_interface 不可用")
            return None

        try:
            # 根据C++接口文档: Result get_speed(SOCKETFD socketFd, int &speed)
            # Python包装器返回 [result_code, speed_value]
            speed_value = 0  # 初始化输出参数

            # 调用API获取速度 - 返回值是列表 [result_code, actual_speed]
            result_list = nrc.get_speed(self.conn.get_socket_fd(), speed_value)

            if isinstance(result_list, list) and len(result_list) >= 2:
                result_code = result_list[0]
                current_speed = result_list[1]

                if result_code == 0:  # SUCCESS
                    print(f"[成功] 当前速度比例: {current_speed}%")
                    return current_speed
                else:
                    print(f"[错误] 获取速度失败，错误代码: {result_code}")
                    return None
            else:
                print("[错误] 获取速度失败：返回数据格式错误")
                return None

        except Exception as e:
            print(f"[错误] 获取速度异常: {e}")
            return None
