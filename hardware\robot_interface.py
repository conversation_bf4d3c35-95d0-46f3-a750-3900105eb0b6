# hardware/robot_interface.py
"""
INEXBOT机器人接口模块 - 向后兼容版本

[警告] 重要提示：此文件已重构为向后兼容接口
实际实现已移至 src/robot/ 模块，采用更清晰的架构设计。

新的模块化架构：
- src/core/: 核心功能（连接、错误处理、验证）
- src/robot/: 机器人控制（伺服、运动、状态）
- 现有代码无需修改，导入路径保持不变

重构收益：
[成功] 代码模块化，易于维护
[成功] 开发环境友好，无硬件也能运行
[成功] 增强的错误处理和状态检查
[成功] 完全向后兼容

作者: Augment Agent
日期: 2025-07-15
"""

# 导入新的模块化实现
from src.robot.interface import RobotInterface

# 保持向后兼容性 - 现有代码无需修改
# from hardware.robot_interface import RobotInterface 仍然有效

__all__ = ['RobotInterface']