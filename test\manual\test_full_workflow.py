# test_diagnosis.py
"""
机器人诊断测试脚本

该脚本用于诊断机器人连接后的状态问题，
按照INEXBOT官方文档的建议顺序进行操作。
"""

import time
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))

from src.robot.interface import RobotInterface
from config import ROBOT_IP, ROBOT_PORT


def run_diagnosis_test():
    """
    执行机器人诊断测试
    """
    robot = None
    
    try:
        print("=" * 60)
        print("机器人诊断测试")
        print("=" * 60)
        
        # 1. 连接机器人
        print("步骤1: 连接机器人")
        robot = RobotInterface(ROBOT_IP, ROBOT_PORT)
        print("[成功] 机器人连接成功")
        print("-" * 40)
        
        # 2. 检查基础状态
        print("步骤2: 检查基础状态")
        
        # 检查连接状态
        conn_status = robot.get_connection_status()
        print(f"连接状态: {conn_status}")
        
        # 检查运行状态
        running_state = robot.get_robot_running_state()
        if running_state:
            print(f"运行状态: {running_state['description']} (代码: {running_state['status']})")
        else:
            print("[警告] 无法获取运行状态")
        
        # 检查伺服状态
        servo_state = robot.get_servo_state()
        if servo_state:
            print(f"伺服状态: {servo_state['description']} (代码: {servo_state['status_code']})")
        else:
            print("[警告] 无法获取伺服状态")
        
        print("-" * 40)
        
        # 3. 尝试清除错误
        print("步骤3: 清除可能的错误状态")
        if robot.clear_error():
            print("[成功] 错误状态已清除")
        else:
            print("[警告] 清除错误失败或无错误需要清除")
        
        time.sleep(1)
        print("-" * 40)
        
        # 4. 重新检查状态
        print("步骤4: 重新检查状态")
        
        running_state = robot.get_robot_running_state()
        if running_state:
            print(f"运行状态: {running_state['description']} (代码: {running_state['status']})")
        
        servo_state = robot.get_servo_state()
        if servo_state:
            print(f"伺服状态: {servo_state['description']} (代码: {servo_state['status_code']})")
        
        print("-" * 40)
        
        # 5. 尝试设置伺服为就绪状态
        print("步骤5: 尝试设置伺服为就绪状态")
        
        # 根据官方文档，先设置伺服为就绪状态(1)
        print("设置伺服为就绪状态...")
        if robot.enable_servos():  # 这实际上调用 set_servo_state(1)
            print("[成功] 伺服设置为就绪状态成功")
            
            # 等待状态更新
            time.sleep(2)
            
            # 检查伺服状态
            servo_state = robot.get_servo_state()
            if servo_state:
                print(f"当前伺服状态: {servo_state['description']} (代码: {servo_state['status_code']})")
                
                # 如果伺服状态为就绪(1)，尝试上电
                if servo_state['status_code'] == 1:
                    print("尝试机器人上电...")
                    if robot.set_servo_poweron():
                        print("[成功] 机器人上电成功")
                        time.sleep(2)
                        
                        # 再次检查状态
                        servo_state = robot.get_servo_state()
                        if servo_state:
                            print(f"上电后伺服状态: {servo_state['description']} (代码: {servo_state['status_code']})")
                    else:
                        print("[警告] 机器人上电失败")
                        
        else:
            print("[警告] 设置伺服为就绪状态失败")
        
        print("-" * 40)
        
        # 6. 尝试获取位置（如果伺服状态允许）
        print("步骤6: 尝试获取位置信息")
        
        servo_state = robot.get_servo_state()
        if servo_state and servo_state['status_code'] in [1, 3]:  # 就绪或运行状态
            print("伺服状态允许，尝试获取位置...")
            
            # 尝试不同的坐标系
            coord_types = {
                0: "关节坐标系",
                1: "直角坐标系", 
                2: "工具坐标系",
                3: "用户坐标系"
            }
            
            for coord_type, coord_name in coord_types.items():
                position = robot.get_current_position(coord_type)
                if position:
                    print(f"[成功] {coord_name}: {position}")
                    break
                else:
                    print(f"[警告] 无法获取{coord_name}")
        else:
            print("[警告] 伺服状态不允许获取位置")
        
        print("-" * 40)
        
        # 7. 速度控制测试
        print("步骤7: 速度控制测试")
        
        current_speed = robot.get_speed()
        if current_speed is not None:
            print(f"[成功] 当前速度: {current_speed}%")
            
            # 尝试设置速度
            if robot.set_speed(50):
                print("[成功] 速度设置成功")
                new_speed = robot.get_speed()
                if new_speed is not None:
                    print(f"[成功] 新速度: {new_speed}%")
            else:
                print("[警告] 速度设置失败")
        else:
            print("[警告] 无法获取当前速度")
        
        print("-" * 40)
        print("诊断测试完成")
        
    except Exception as e:
        print(f"诊断测试过程中发生错误: {e}")
        
    finally:
        if robot:
            print("-" * 40)
            print("正在安全关闭...")
            
            # 尝试下电
            servo_state = robot.get_servo_state()
            if servo_state and servo_state['status_code'] == 3:  # 运行状态
                print("尝试机器人下电...")
                robot.set_servo_poweroff()
                time.sleep(1)
            
            # 尝试关闭伺服
            print("尝试关闭伺服...")
            robot.disable_servos()
            
            # 断开连接
            print("断开连接...")
            robot.disconnect()
            
        print("=" * 60)
        print("诊断完成")
        print("=" * 60)


def main():
    """
    主函数
    """
    print("机器人诊断测试")
    print("该测试将按照官方文档建议的顺序检查机器人状态")
    print()
    
    user_input = input("确认开始诊断测试，输入 'yes' 继续: ")
    if user_input.lower() != 'yes':
        print("诊断测试已取消")
        return
    
    run_diagnosis_test()


if __name__ == "__main__":
    main()
