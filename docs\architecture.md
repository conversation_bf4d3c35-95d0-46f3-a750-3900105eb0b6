# 系统架构文档

## 概述

本文档描述了重构后的机器人3D打印控制系统的架构设计，包括模块划分、依赖关系和设计原则。

## 架构目标

1. **模块化**: 清晰的模块边界，单一职责原则
2. **可维护性**: 代码组织清晰，易于理解和修改
3. **可测试性**: 支持单元测试和集成测试
4. **开发友好**: 无硬件环境下也能正常开发
5. **向后兼容**: 保持现有API接口不变

## 模块架构

### 分层设计

```
┌─────────────────────────────────────────┐
│              应用层 (Application)        │
│  main.py, scripts/, 用户接口            │
└─────────────────────────────────────────┘
                    ↓
┌─────────────────────────────────────────┐
│              业务层 (Business)           │
│  src/robot/, src/extruder/              │
│  具体的机器人和挤出机控制逻辑             │
└─────────────────────────────────────────┘
                    ↓
┌─────────────────────────────────────────┐
│              核心层 (Core)               │
│  src/core/                              │
│  连接管理、错误处理、参数验证             │
└─────────────────────────────────────────┘
                    ↓
┌─────────────────────────────────────────┐
│              基础层 (Foundation)         │
│  lib/nrc_interface.py, lib/nrc_host.pyd │
│  INEXBOT SDK 和第三方库                 │
└─────────────────────────────────────────┘
```

### 模块详细说明

#### 1. 核心层 (src/core/)

**职责**: 提供基础的系统功能

- **connection.py**: 连接管理
  - 机器人连接/断开
  - 连接状态监控
  - 开发模式处理
  
- **error_handler.py**: 错误处理
  - 统一错误处理机制
  - 安全调用封装
  - 错误清除功能
  
- **validators.py**: 参数验证
  - 输入参数验证
  - 数据类型检查
  - 范围验证

#### 2. 业务层 (src/robot/)

**职责**: 实现具体的机器人控制功能

- **base.py**: 基础功能
  - 位置获取
  - 连接状态查询
  - 基础信息获取
  
- **servo.py**: 伺服控制
  - 伺服使能/关闭
  - 上电/下电
  - 状态检查
  
- **motion.py**: 运动控制
  - 线性运动
  - 关节运动
  - 速度控制
  
- **status.py**: 状态监控
  - 运行状态查询
  - 系统健康检查
  - 连续监控
  
- **interface.py**: 统一接口
  - 组合各个模块
  - 提供统一API
  - 向后兼容

#### 3. 应用层

**职责**: 用户接口和业务流程编排

- **main.py**: 主程序入口
- **scripts/**: 工具脚本
- **test/**: 测试程序

## 设计原则

### 1. 单一职责原则 (SRP)

每个模块只负责一个特定的功能领域：
- 连接管理只处理连接相关逻辑
- 伺服控制只处理伺服相关操作
- 运动控制只处理运动相关功能

### 2. 依赖注入 (DI)

模块间通过构造函数注入依赖：
```python
class MotionController:
    def __init__(self, connection_manager, servo_controller, error_handler):
        self.conn = connection_manager
        self.servo = servo_controller
        self.error_handler = error_handler
```

### 3. 接口隔离原则 (ISP)

提供专用的接口，避免臃肿的大接口：
- 伺服控制有专门的ServoController
- 运动控制有专门的MotionController
- 状态监控有专门的StatusMonitor

### 4. 开闭原则 (OCP)

对扩展开放，对修改关闭：
- 新功能通过添加新模块实现
- 现有模块接口保持稳定
- 通过组合而非继承扩展功能

## 数据流

### 典型的运动控制流程

```
用户调用 → RobotInterface.move_linear()
    ↓
检查参数 → ParameterValidator.validate_position()
    ↓
检查伺服 → ServoController.check_servo_ready_for_motion()
    ↓
执行运动 → MotionController.move_linear()
    ↓
硬件调用 → nrc.robot_movel()
```

### 错误处理流程

```
硬件错误 → ErrorHandler.safe_call()
    ↓
错误分类 → 连接错误 / 参数错误 / 硬件错误
    ↓
错误处理 → 重试 / 降级 / 报告
    ↓
用户反馈 → 返回结果 / 抛出异常
```

## 测试策略

### 测试分层

1. **单元测试** (test/unit/)
   - 测试单个模块功能
   - 使用Mock对象模拟依赖
   - 专注于逻辑正确性

2. **集成测试** (test/integration/)
   - 测试模块间协作
   - 端到端功能验证
   - 在开发环境下运行

3. **手动测试** (test/manual/)
   - 需要真实硬件
   - 完整功能验证
   - 性能和稳定性测试

### 开发模式支持

系统支持开发模式，无硬件连接时：
- 自动检测连接失败
- 切换到模拟模式
- 返回模拟数据
- 提供友好提示

## 向后兼容性

### 兼容性策略

1. **保持原有导入路径**:
   ```python
   from hardware.robot_interface import RobotInterface  # 仍然有效
   ```

2. **保持原有API接口**:
   - 方法名称不变
   - 参数列表不变
   - 返回值格式不变

3. **透明重定向**:
   ```python
   # hardware/robot_interface.py
   from src.robot.interface import RobotInterface
   ```

### 迁移路径

用户可以选择性地迁移到新接口：

1. **立即使用**: 无需修改现有代码
2. **逐步迁移**: 可以逐步使用新的模块化接口
3. **完全迁移**: 直接使用src/模块获得更好的功能

## 扩展性

### 添加新功能

1. **新的控制模块**: 在src/robot/下添加新模块
2. **新的硬件支持**: 在src/下添加新的硬件目录
3. **新的工具**: 在scripts/下添加新脚本

### 示例：添加视觉模块

```python
# src/vision/camera.py
class CameraController:
    def __init__(self, connection_manager):
        self.conn = connection_manager
    
    def capture_image(self):
        # 实现图像捕获
        pass

# src/robot/interface.py
class RobotInterface:
    def __init__(self, ip, port):
        # 现有初始化...
        self.vision = CameraController(self.conn)  # 添加视觉模块
```

## 总结

重构后的架构具有以下优势：

1. **清晰的模块边界**: 每个模块职责明确
2. **良好的可测试性**: 支持各种级别的测试
3. **开发友好**: 无硬件也能正常开发
4. **完全向后兼容**: 现有代码无需修改
5. **易于扩展**: 新功能可以轻松添加

这种架构设计为项目的长期发展奠定了坚实的基础。
