# verify_handover.py
"""
项目交接验证脚本

该脚本用于验证handover.md文档中提到的所有功能和文件是否正常工作。
在新的对话会话开始时运行此脚本，可以快速确认项目状态。
"""

import os
import sys
import importlib.util


def check_file_exists(filepath, description):
    """检查文件是否存在"""
    if os.path.exists(filepath):
        print(f"[成功] {description}: {filepath}")
        return True
    else:
        print(f"[错误] {description}: {filepath} [文件不存在]")
        return False


def check_import(module_name, description):
    """检查模块是否可以导入"""
    try:
        if '.' in module_name:
            # 处理子模块导入
            parts = module_name.split('.')
            module = __import__(module_name)
            for part in parts[1:]:
                module = getattr(module, part)
        else:
            module = __import__(module_name)
        print(f"[成功] {description}: {module_name}")
        return True
    except ImportError as e:
        print(f"[错误] {description}: {module_name} [导入失败: {e}]")
        return False
    except Exception as e:
        print(f"[警告] {description}: {module_name} [警告: {e}]")
        return False


def check_config():
    """检查配置文件"""
    try:
        import config
        print(f"[成功] 配置文件导入成功")
        
        # 检查关键配置项
        required_configs = ['ROBOT_IP', 'ROBOT_PORT', 'KLIPPER_IP']
        for config_name in required_configs:
            if hasattr(config, config_name):
                value = getattr(config, config_name)
                print(f"  - {config_name}: {value}")
            else:
                print(f"  [错误] 缺少配置项: {config_name}")
                return False
        
        return True
    except Exception as e:
        print(f"[错误] 配置文件检查失败: {e}")
        return False


def check_interfaces():
    """检查硬件接口"""
    try:
        from hardware.robot_interface import RobotInterface
        from hardware.extruder_interface import ExtruderInterface
        print("[成功] 硬件接口导入成功")
        
        # 检查关键方法是否存在
        robot_methods = [
            'disconnect', 'enable_servos', 'disable_servos',
            'get_robot_running_state', 'get_servo_state', 'clear_error',
            'get_current_position', 'move_linear', 'robot_movej',
            'set_speed', 'get_speed', 'set_servo_poweron', 'set_servo_poweroff',
            'is_robot_connected', 'get_connection_status', 'get_socket_fd'
        ]
        
        extruder_methods = [
            'get_status', 'get_temperatures', 'set_heater_temperature',
            'wait_for_temperature', 'extrude', 'turn_off_heaters'
        ]
        
        # 检查RobotInterface方法
        missing_robot_methods = []
        for method in robot_methods:
            if not hasattr(RobotInterface, method):
                missing_robot_methods.append(method)
        
        if missing_robot_methods:
            print(f"  [警告] RobotInterface缺少方法: {missing_robot_methods}")
        else:
            print("  [成功] RobotInterface所有方法存在")
        
        # 检查ExtruderInterface方法
        missing_extruder_methods = []
        for method in extruder_methods:
            if not hasattr(ExtruderInterface, method):
                missing_extruder_methods.append(method)
        
        if missing_extruder_methods:
            print(f"  [警告] ExtruderInterface缺少方法: {missing_extruder_methods}")
        else:
            print("  [成功] ExtruderInterface所有方法存在")
        
        return len(missing_robot_methods) == 0 and len(missing_extruder_methods) == 0
        
    except Exception as e:
        print(f"[错误] 硬件接口检查失败: {e}")
        return False


def main():
    """主验证函数"""
    print("=" * 60)
    print("机器人3D打印控制系统 - 项目交接验证")
    print("=" * 60)
    
    all_checks_passed = True
    
    # 1. 检查核心文件
    print("\n1. 核心文件检查:")
    core_files = [
        ("README.md", "项目主文档"),
        ("handover.md", "项目交接文档"),
        ("config.py", "配置文件"),
        ("main.py", "基础连接测试"),
        ("nrc_host.pyd", "INEXBOT SDK库"),
        ("nrc_interface.py", "INEXBOT SDK接口"),
        ("hardware/__init__.py", "硬件模块初始化"),
        ("hardware/robot_interface.py", "机械臂接口"),
        ("hardware/extruder_interface.py", "挤出机接口"),
    ]
    
    for filepath, description in core_files:
        if not check_file_exists(filepath, description):
            all_checks_passed = False
    
    # 2. 检查测试脚本
    print("\n2. 测试脚本检查:")
    test_files = [
        ("test_motion.py", "运动控制测试"),
        ("test_extruder.py", "挤出机测试"),
        ("test_robot_enhanced.py", "增强功能测试"),
        ("test_diagnosis.py", "诊断脚本"),
    ]
    
    for filepath, description in test_files:
        if not check_file_exists(filepath, description):
            all_checks_passed = False
    
    # 3. 检查依赖导入
    print("\n3. 依赖导入检查:")
    dependencies = [
        ("requests", "HTTP通信库"),
        ("time", "时间处理"),
        ("json", "JSON处理"),
    ]
    
    for module_name, description in dependencies:
        if not check_import(module_name, description):
            all_checks_passed = False
    
    # 4. 检查配置
    print("\n4. 配置检查:")
    if not check_config():
        all_checks_passed = False
    
    # 5. 检查硬件接口
    print("\n5. 硬件接口检查:")
    if not check_interfaces():
        all_checks_passed = False
    
    # 6. 检查INEXBOT SDK
    print("\n6. INEXBOT SDK检查:")
    try:
        import nrc_interface as nrc
        print("[成功] INEXBOT SDK导入成功")
        
        # 检查关键函数
        key_functions = [
            'connect_robot', 'disconnect_robot', 'get_connection_status',
            'set_servo_state', 'get_servo_state', 'clear_error',
            'get_current_position', 'robot_movel', 'robot_movej',
            'set_speed', 'get_speed', 'get_robot_running_state'
        ]
        
        missing_functions = []
        for func_name in key_functions:
            if not hasattr(nrc, func_name):
                missing_functions.append(func_name)
        
        if missing_functions:
            print(f"  [警告] INEXBOT SDK缺少函数: {missing_functions}")
            all_checks_passed = False
        else:
            print("  [成功] INEXBOT SDK所有关键函数存在")
            
    except Exception as e:
        print(f"[错误] INEXBOT SDK检查失败: {e}")
        all_checks_passed = False
    
    # 总结
    print("\n" + "=" * 60)
    if all_checks_passed:
        print("[庆祝] 所有检查通过！项目状态正常，可以开始开发。")
        print("\n建议下一步操作:")
        print("1. 运行 'python main.py' 测试机器人连接")
        print("2. 运行 'python test_diagnosis.py' 进行全面诊断")
        print("3. 查看 'handover.md' 了解详细的项目状态")
    else:
        print("❌ 部分检查失败，请根据上述错误信息修复问题。")
        print("\n常见解决方案:")
        print("1. 确保所有文件都在正确位置")
        print("2. 安装缺失的依赖: pip install requests")
        print("3. 检查INEXBOT SDK文件是否完整")
    
    print("=" * 60)
    
    return all_checks_passed


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
