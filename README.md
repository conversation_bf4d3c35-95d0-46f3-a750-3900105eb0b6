# 机器人3D打印控制系统

一个基于INEXBOT机械臂和Klipper挤出机的完整3D打印控制系统，采用三层软件架构设计。

## 🆕 重构更新 (2025-07-15)

**代码已完成全面重构，采用模块化架构设计！**

### 重构收益
- ✅ **模块化架构**: 代码组织清晰，易于维护和扩展
- ✅ **开发友好**: 无硬件连接也能正常开发和测试
- ✅ **增强功能**: 更强的错误处理和状态检查
- ✅ **向后兼容**: 现有代码无需修改即可使用
- ✅ **测试完善**: 完整的测试体系和文档

## 📋 目录

- [项目概述](#项目概述)
- [系统架构](#系统架构)
- [功能特性](#功能特性)
- [安装配置](#安装配置)
- [快速开始](#快速开始)
- [API文档](#api文档)
- [测试指南](#测试指南)
- [安全注意事项](#安全注意事项)
- [故障排除](#故障排除)
- [项目结构](#项目结构)

## 🎯 项目概述

本项目旨在构建一个完整的机器人3D打印控制系统，将INEXBOT机械臂与Klipper固件的3D打印挤出机相结合。系统能够解析G-code指令，协调控制机械臂运动和挤出机操作，实现精确的3D打印功能。

### 核心目标

- **精确控制**：实现机械臂与挤出机的精确协调控制
- **安全可靠**：提供完整的错误处理和安全保护机制
- **易于扩展**：采用模块化设计，便于功能扩展和维护
- **实时监控**：支持实时状态监控和动态参数调整

## 🏗️ 系统架构

本系统采用三层软件架构设计：

```
┌─────────────────────────────────────────┐
│              UI层 (ui/)                 │
│        用户图形界面，交互和状态显示        │
│              (未来实现)                  │
└─────────────────────────────────────────┘
                    ↕
┌─────────────────────────────────────────┐
│           核心逻辑层 (core/)             │
│      G-code解析，运动规划，协调控制       │
│              (未来实现)                  │
└─────────────────────────────────────────┘
                    ↕
┌─────────────────────────────────────────┐
│         硬件通信层 (hardware/)           │
│    ├─ RobotInterface (机械臂控制)       │
│    └─ ExtruderInterface (挤出机控制)    │
│              (已完成)                   │
└─────────────────────────────────────────┘
```

### 当前实现状态

- ✅ **硬件通信层**：完整实现机械臂和挤出机控制接口
- 🔄 **核心逻辑层**：规划中，将实现G-code解析和协调控制
- 🔄 **UI层**：规划中，将提供图形化用户界面

## ⭐ 功能特性

### 🤖 机械臂控制 (RobotInterface)官方文档接口https://doc.hmilib.inexbot.coision.cn/nrc__interface_8h.html 

#### 基础功能
- ✅ 连接管理和状态查询
- ✅ 伺服电机控制（使能/关闭）
- ✅ 实时位置获取（笛卡尔坐标）
- ✅ 线性运动控制（MoveL）

#### 增强功能
- ✅ 运行状态监控（停止/运行/暂停/错误/急停/准备）
- ✅ 错误处理和自动恢复
- ✅ 动态速度控制（0.1-100%）
- ✅ 关节运动控制（MoveJ）
- ✅ 伺服状态查询

#### 🆕 增强伺服控制功能
- ✅ **智能伺服使能**: 自动状态检查、错误清除、安全验证
- ✅ **报警状态处理**: 自动检测并处理伺服报警状态（状态2）
- ✅ **运动前安全检查**: 运动指令前自动验证伺服状态
- ✅ **详细状态反馈**: 提供完整的操作过程和结果信息
- ✅ **符合官方规范**: 严格按照INEXBOT官方文档实现

### 🔥 挤出机控制 (ExtruderInterface)

#### 温度管理
- ✅ 实时温度监控（挤出头和热床）
- ✅ 目标温度设置（非阻塞）
- ✅ 温度等待功能（阻塞，带超时保护）
- ✅ 自动加热器关闭

#### 挤出控制
- ✅ 精确挤出控制（量和速度）
- ✅ G-code指令发送
- ✅ HTTP通信和错误处理

## 🚀 安装配置

### 系统要求

- Python 3.8+
- Windows/Linux操作系统
- INEXBOT机械臂控制器
- 运行Klipper/Moonraker的3D打印机

### 依赖安装

```bash
# 安装Python依赖
pip install requests

# 确保INEXBOT SDK文件存在
# - nrc_host.pyd
# - nrc_interface.py
```

### 配置设置

编辑 `config.py` 文件，设置设备IP地址：

```python
# INEXBOT 机械臂配置
ROBOT_IP = "************"    # 替换为实际机器人IP
ROBOT_PORT = 6001            # INEXBOT机器人通常使用6001端口

# Klipper 挤出机配置
KLIPPER_IP = "************"  # 替换为Klipper设备IP
KLIPPER_PORT = 7125          # Moonraker端口

# 系统配置
DEBUG_MODE = True            # 调试模式
CONNECTION_TIMEOUT = 10      # 连接超时时间
RETRY_ATTEMPTS = 3           # 重试次数
```

## ⚠️ 重要：正确的操作顺序

根据INEXBOT官方文档和实际测试，机器人控制必须遵循以下操作顺序：

### 📋 增强操作流程（推荐）

🆕 **使用增强伺服控制功能，自动处理错误和状态检查：**

1. **连接机器人** - `RobotInterface(ip, port)`
2. **智能伺服使能** - `robot.enable_servos()` (自动清除错误、状态检查)
3. **检查运动准备** - `robot.check_servo_ready_for_motion()`
4. **执行运动控制** - `robot.move_linear()` 或 `robot.robot_movej()` (自动状态验证)
5. **智能伺服关闭** - `robot.disable_servos()` (智能下电处理)
6. **断开连接** - `robot.disconnect()`

### 📋 传统操作流程（仍然支持）

1. **连接机器人** - `RobotInterface(ip, port)`
2. **清除错误状态** - `robot.clear_error()`
3. **检查机器人状态** - `robot.get_robot_running_state()`
4. **检查伺服状态** - `robot.get_servo_state()`
5. **设置伺服就绪** - `robot.enable_servos()` (设置为就绪状态)
6. **机器人上电** - `robot.set_servo_poweron()` (如果需要运行状态)
7. **执行运动控制** - `robot.move_linear()` 或 `robot.robot_movej()`
8. **安全关闭** - 按相反顺序关闭

### ⚠️ 常见错误（已通过增强功能解决）

- **直接使能伺服**: 可能导致 "错误代码: -1" → ✅ **已解决**: 自动错误清除和状态检查
- **跳过状态检查**: 无法判断机器人是否准备就绪 → ✅ **已解决**: 自动状态验证
- **忽略错误清除**: 之前的错误状态会影响后续操作 → ✅ **已解决**: 预防性错误清除
- **伺服报警状态**: 状态2无法直接使能 → ✅ **已解决**: 自动检测并处理报警状态

## 🏃 快速开始

### 1. 基础连接测试

```bash
# 测试机器人连接
python main.py

# 测试挤出机连接
python test_extruder.py
```

### 2. 运动控制测试

#### 🆕 使用新的测试系统
```bash
# 运行所有开发环境测试（无需硬件）
python scripts/run_tests.py dev

# 运行单元测试
python scripts/run_tests.py unit

# 运行集成测试
python scripts/run_tests.py integration

# 查看手动测试（需要硬件）
python scripts/run_tests.py manual

# 运行重构演示
python scripts/demo.py
```

#### 传统测试方式（仍然支持）
```bash
# 手动测试（需要硬件连接）
python test/manual/test_hardware_connection.py
python test/integration/test_robot_complete.py
python test/unit/test_servo.py
```

### 3. 代码示例

#### 🆕 增强机器人控制示例（推荐）

```python
from hardware.robot_interface import RobotInterface
from config import ROBOT_IP, ROBOT_PORT

# 连接机器人
robot = RobotInterface(ROBOT_IP, ROBOT_PORT)

# 🆕 使用增强伺服控制 - 自动处理错误和状态检查
if robot.enable_servos():  # 自动清除错误、检查状态、处理报警
    print("✓ 伺服使能成功")

    # 检查是否适合执行运动
    if robot.check_servo_ready_for_motion():
        print("✓ 伺服状态适合运动")

        # 获取当前位置
        current_pos = robot.get_current_position(coord_type=1)
        print(f"当前位置: {current_pos}")

        # 执行运动控制（自动验证伺服状态）
        target_pos = [100, 200, 300, 0, 0, 0]  # X,Y,Z,A,B,C
        if robot.move_linear(target_pos, velocity=50, blending_radius=1):
            print("✓ 运动执行成功")

        # 设置速度
        robot.set_speed(75)  # 75%速度（整数）

    # 智能伺服关闭（自动处理下电）
    robot.disable_servos()
else:
    print("✗ 伺服使能失败，请检查机器人状态")

# 断开连接
robot.disconnect()
```

#### 传统机器人控制示例（仍然支持）

```python
from hardware.robot_interface import RobotInterface
from config import ROBOT_IP, ROBOT_PORT

# 连接机器人
robot = RobotInterface(ROBOT_IP, ROBOT_PORT)

# ⚠️ 重要：按照正确的操作顺序进行控制
# 1. 清除可能的错误状态
robot.clear_error()

# 2. 检查机器人状态
running_state = robot.get_robot_running_state()
print(f"运行状态: {running_state['description']}")

servo_state = robot.get_servo_state()
print(f"伺服状态: {servo_state['description']}")

# 3. 设置伺服为就绪状态
robot.enable_servos()

# 4. 如果需要，上电到运行状态
if servo_state['status_code'] == 1:  # 就绪状态
    robot.set_servo_poweron()

# 5. 获取当前位置
current_pos = robot.get_current_position(coord_type=1)  # 直角坐标系
print(f"当前位置: {current_pos}")

# 6. 执行运动控制
target_pos = [100, 200, 300, 0, 0, 0]  # X,Y,Z,A,B,C
robot.move_linear(target_pos, velocity=50, blending_radius=1)

# 7. 设置速度
robot.set_speed(75)  # 75%速度（整数）

# 8. 安全关闭
robot.set_servo_poweroff()  # 下电（如果之前上电了）
robot.disable_servos()      # 关闭伺服
robot.disconnect()          # 断开连接
```

#### 挤出机控制示例

```python
from hardware.extruder_interface import ExtruderInterface
from config import KLIPPER_IP

# 连接挤出机
extruder = ExtruderInterface(KLIPPER_IP)

# 检查连接
if extruder.get_status():
    print("挤出机连接成功")

# 设置温度并等待
extruder.set_heater_temperature(200.0)
extruder.wait_for_temperature(200.0)

# 执行挤出
extruder.extrude(amount=5.0, speed=300)

# 关闭加热器
extruder.turn_off_heaters()
```

## 📚 API文档

### RobotInterface API

#### 连接管理

```python
# 初始化连接
robot = RobotInterface(ip, port)

# 检查连接状态
is_connected = robot.is_robot_connected()
status_code = robot.get_connection_status()

# 断开连接
robot.disconnect()
```

#### 伺服控制

```python
# 使能/关闭伺服
robot.enable_servos()
robot.disable_servos()

# 获取伺服状态
servo_state = robot.get_servo_state()
# 返回: {"enabled": bool, "status_code": int, "description": str}
# 状态码: 0=停止, 1=就绪, 2=报警, 3=运行
```

#### 状态查询

```python
# 获取运行状态
running_state = robot.get_robot_running_state()
# 返回: {"status": int, "description": str}
# 状态码: 0=停止, 1=运行, 2=暂停, 3=错误, 4=急停, 5=准备
```

#### 位置控制

```python
# 获取当前位置
position = robot.get_current_position()
# 返回: [X, Y, Z, A, B, C] (mm, deg)

# 线性运动
success = robot.move_linear(target_pos, velocity, blending_radius)

# 关节运动
success = robot.robot_movej(target_pos, velocity, blending_radius)
```

#### 速度控制

```python
# 设置速度比例 (1-100%)
robot.set_speed(75)  # 整数类型

# 获取当前速度比例
current_speed = robot.get_speed()  # 返回整数
```

#### 电源控制

```python
# 机器人上电（需要先设置伺服为就绪状态）
robot.set_servo_state(1)  # 设置为就绪状态
robot.set_servo_poweron()

# 机器人下电
robot.set_servo_poweroff()
```

#### 错误处理

```python
# 清除错误状态
success = robot.clear_error()
```

### ExtruderInterface API

#### 连接管理

```python
# 初始化连接
extruder = ExtruderInterface(ip)

# 检查连接状态
is_connected = extruder.get_status()
```

#### 温度管理

```python
# 获取温度信息
temps = extruder.get_temperatures()
# 返回: {
#   "extruder": {"actual": float, "target": float},
#   "heater_bed": {"actual": float, "target": float}
# }

# 设置目标温度（非阻塞）
extruder.set_heater_temperature(target_temp)

# 等待达到目标温度（阻塞）
extruder.wait_for_temperature(target_temp, timeout=180)

# 关闭所有加热器
extruder.turn_off_heaters()
```

#### 挤出控制

```python
# 执行挤出动作
success = extruder.extrude(amount, speed)
# amount: 挤出量(mm), speed: 速度(mm/min)

# 发送自定义G-code
success = extruder._send_gcode("G1 E5 F300")
```

## 🧪 测试指南

### 测试脚本说明

| 脚本名称 | 功能描述 | 测试内容 |
|---------|---------|---------|
| `main.py` | 基础连接测试 | 机器人连接、状态查询 |
| `test_motion.py` | 运动控制测试 | 伺服控制、位置读取、线性运动 |
| `test_extruder.py` | 挤出机测试 | 温度控制、加热、挤出功能 |
| `test_robot_enhanced.py` | 增强功能测试 | 状态监控、错误处理、速度控制 |

### 测试执行顺序

#### 阶段1：基础功能验证
```bash
# 1. 测试机器人连接
python main.py

# 2. 测试挤出机连接
python test_extruder.py
```

#### 阶段2：运动功能测试
```bash
# 3. 测试基础运动功能
python test_motion.py

# 4. 测试增强功能（推荐）
python test_robot_enhanced.py
```

### 测试前准备

1. **硬件准备**
   - 确保机器人和挤出机正常启动
   - 检查网络连接
   - 确保工作空间安全

2. **软件准备**
   - 更新 `config.py` 中的IP地址
   - 确保所有依赖已安装
   - 检查INEXBOT SDK文件

3. **安全检查**
   - 清空机器人工作空间
   - 准备紧急停止按钮
   - 确认安全操作距离

## ⚠️ 安全注意事项

### 🚨 重要警告

- **始终保持安全距离**：操作期间人员应远离机器人工作空间
- **紧急停止准备**：确保紧急停止按钮随时可用
- **工作空间检查**：运行前清空所有障碍物
- **参数验证**：仔细检查所有运动参数和温度设置

### 🛡️ 安全措施

#### 机器人安全
- 运动前检查目标位置合理性
- 使用适当的运动速度（建议≤50mm/s）
- 设置合理的路径平滑参数
- 定期检查机器人状态

#### 挤出机安全
- 设置合适的温度范围（PLA: 190-210°C）
- 避免长时间高温加热
- 确保通风良好
- 使用耐热材料和工具

#### 软件安全
- 实现运动边界检查
- 设置超时保护机制
- 建立错误恢复流程
- 记录操作日志

## 🔧 故障排除

### 常见问题

#### 连接问题

**问题**: 机器人连接失败
```
解决方案:
1. 检查IP地址和端口配置
2. 确认机器人控制器网络连接
3. 检查防火墙设置
4. 验证INEXBOT SDK文件完整性
```

**问题**: Klipper连接失败
```
解决方案:
1. 确认Moonraker服务运行状态
2. 检查Klipper设备IP地址
3. 验证网络连通性
4. 检查Moonraker配置文件
```

#### 运动问题

**问题**: 机器人运动异常
```
解决方案:
1. 检查伺服使能状态
2. 验证目标位置合理性
3. 检查运动参数设置
4. 清除错误状态后重试
```

**问题**: 运动精度不足
```
解决方案:
1. 降低运动速度
2. 调整路径平滑参数
3. 检查机器人标定状态
4. 验证坐标系设置
```

#### 温度问题

**问题**: 加热器无法达到目标温度
```
解决方案:
1. 检查加热器硬件连接
2. 验证温度传感器状态
3. 检查电源供应
4. 调整PID参数
```

### 错误代码参考

#### 机器人错误代码
- `0`: 成功
- `-1`: 连接失败
- `-2`: 参数错误
- `-3`: 运动超限
- `-4`: 伺服未使能

#### HTTP错误代码
- `200`: 请求成功
- `404`: 端点不存在
- `500`: 服务器内部错误
- `timeout`: 请求超时

### 调试技巧

1. **启用调试模式**
   ```python
   # 在config.py中设置
   DEBUG_MODE = True
   ```

2. **查看详细日志**
   ```python
   # 添加日志输出
   import logging
   logging.basicConfig(level=logging.DEBUG)
   ```

3. **分步测试**
   - 先测试连接，再测试功能
   - 使用小幅度运动验证
   - 逐步增加复杂度

## 📁 项目结构

### 🆕 新的模块化结构
```
Host_Computer/
├── src/                         # 源代码目录
│   ├── core/                    # 核心模块
│   │   ├── connection.py        # 连接管理
│   │   ├── error_handler.py     # 错误处理
│   │   └── validators.py        # 参数验证
│   ├── robot/                   # 机器人控制模块
│   │   ├── base.py              # 基础接口
│   │   ├── servo.py             # 伺服控制
│   │   ├── motion.py            # 运动控制
│   │   ├── status.py            # 状态监控
│   │   └── interface.py         # 统一接口
│   ├── extruder/                # 挤出机控制模块
│   │   └── interface.py         # 挤出机接口
│   └── utils/                   # 工具模块
├── test/                        # 测试目录
│   ├── unit/                    # 单元测试
│   ├── integration/             # 集成测试
│   ├── manual/                  # 手动测试（需要硬件）
│   └── fixtures/                # 测试工具
├── hardware/                    # 向后兼容接口
│   ├── robot_interface.py       # 兼容性接口
│   └── extruder_interface.py    # 兼容性接口
├── lib/                         # 第三方库
│   ├── nrc_interface.py         # INEXBOT SDK
│   └── nrc_host.pyd             # INEXBOT SDK核心
├── config/                      # 配置文件
├── docs/                        # 文档
├── scripts/                     # 工具脚本
├── main.py                      # 主程序入口
└── README.md                    # 项目说明
```

### 向后兼容性
现有代码无需修改，原有的导入方式仍然有效：
```python
from hardware.robot_interface import RobotInterface  # ✅ 仍然有效
from hardware.extruder_interface import ExtruderInterface  # ✅ 仍然有效
```

### 文件说明

#### 核心文件
- **`config.py`**: 系统配置文件，包含设备IP地址和系统参数
- **`main.py`**: 基础连接测试脚本，验证机器人连接功能
- **`nrc_host.pyd`**: INEXBOT官方SDK动态库
- **`nrc_interface.py`**: INEXBOT官方SDK Python接口

#### 硬件通信层
- **`hardware/__init__.py`**: 硬件模块初始化，导出主要接口类
- **`hardware/robot_interface.py`**: 机械臂控制接口，封装所有机器人操作
- **`hardware/extruder_interface.py`**: 挤出机控制接口，封装Klipper通信

#### 测试脚本
- **`test_motion.py`**: 机器人运动控制测试，验证基础运动功能
- **`test_extruder.py`**: 挤出机功能测试，验证温度控制和挤出功能
- **`test_robot_enhanced.py`**: 增强功能测试，验证状态监控和错误处理

## 🔮 未来规划

### 短期目标 (1-2个月)
- [ ] 实现G-code解析器
- [ ] 添加路径规划算法
- [ ] 实现机器人与挤出机协调控制
- [ ] 添加更多运动控制功能（圆弧运动等）

### 中期目标 (3-6个月)
- [ ] 开发图形化用户界面
- [ ] 实现实时监控和可视化
- [ ] 添加打印任务管理功能
- [ ] 支持多种G-code格式

### 长期目标 (6个月以上)
- [ ] 实现自适应打印参数调整
- [ ] 添加机器学习优化算法
- [ ] 支持多机器人协同打印
- [ ] 开发云端监控和控制功能

## 🤝 贡献指南

### 开发环境设置
1. Fork本项目
2. 创建功能分支: `git checkout -b feature/new-feature`
3. 提交更改: `git commit -am 'Add new feature'`
4. 推送分支: `git push origin feature/new-feature`
5. 创建Pull Request

### 代码规范
- 遵循PEP 8 Python代码规范
- 添加完整的文档字符串
- 包含单元测试
- 确保代码安全性

### 测试要求
- 所有新功能必须包含测试
- 确保现有测试通过
- 添加集成测试验证

## 📄 许可证

本项目采用 MIT 许可证 - 详见 [LICENSE](LICENSE) 文件

## 📞 联系方式

- **项目维护者**: [您的姓名]
- **邮箱**: [您的邮箱]
- **项目地址**: [GitHub仓库地址]

## 🙏 致谢

- INEXBOT团队提供的机械臂SDK
- Klipper项目团队
- 所有贡献者和测试用户

---

**⚠️ 免责声明**: 本软件仅供学习和研究使用。使用者应确保遵守所有相关安全规定，并对使用本软件造成的任何损失承担责任。

**📝 最后更新**: 2024年12月

---

*如果这个项目对您有帮助，请给我们一个 ⭐ Star！*
